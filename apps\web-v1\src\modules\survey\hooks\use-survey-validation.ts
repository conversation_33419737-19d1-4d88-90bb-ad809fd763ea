import { useCallback, useMemo } from 'react'
import { VALIDATION_MESSAGES } from '../constants/survey.constants'
import { useSurveyContext } from '../context/survey-context'
import { SurveyErrors, SurveyQuestion } from '../types/survey.types'
import { useSurveyData } from './use-survey-data'

export interface ValidationResult {
  isValid: boolean
  errors: string[]
}

export interface SurveyValidationHook {
  errors: SurveyErrors
  validateQuestion: (question: SurveyQuestion, value?: any) => ValidationResult
  validateStep: (stepId: string) => ValidationResult
  validateCurrentStep: () => ValidationResult
  validateAllSteps: () => ValidationResult
  getQuestionError: (questionId: string) => string | undefined
  getStepErrors: (stepId: string) => Record<string, string>
  hasErrors: boolean
  hasStepErrors: (stepId: string) => boolean
  hasQuestionError: (questionId: string) => boolean
  clearErrors: (stepId?: string) => void
  clearQuestionError: (questionId: string) => void
}

export function useSurveyValidation(): SurveyValidationHook {
  const {
    state,
    dispatch,
    getCurrentStep,
    // validateCurrentStep: contextValidateCurrentStep,
  } = useSurveyContext()

  const {
    getAnswer,
    // isQuestionAnswered
  } = useSurveyData()

  const errors = state.errors

  const validateQuestion = useCallback(
    (question: SurveyQuestion, value?: any): ValidationResult => {
      const questionValue = value !== undefined ? value : getAnswer(question.id)
      const validationErrors: string[] = []

      // Required field validation
      if (question.required) {
        if (
          questionValue === null ||
          questionValue === undefined ||
          questionValue === ''
        ) {
          validationErrors.push(VALIDATION_MESSAGES.REQUIRED)
        } else if (Array.isArray(questionValue) && questionValue.length === 0) {
          validationErrors.push(VALIDATION_MESSAGES.REQUIRED)
        }
      }

      // Type-specific validation
      if (
        questionValue !== null &&
        questionValue !== undefined &&
        questionValue !== ''
      ) {
        switch (question.type) {
          case 'open-ended':
            if (typeof questionValue === 'string') {
              if (
                question.minLength &&
                questionValue.length < question.minLength
              ) {
                validationErrors.push(
                  VALIDATION_MESSAGES.MIN_LENGTH.replace(
                    '{min}',
                    question.minLength.toString(),
                  ),
                )
              }
              if (
                question.maxLength &&
                questionValue.length > question.maxLength
              ) {
                validationErrors.push(
                  VALIDATION_MESSAGES.MAX_LENGTH.replace(
                    '{max}',
                    question.maxLength.toString(),
                  ),
                )
              }
            }
            break

          case 'rating':
            if (typeof questionValue !== 'number') {
              validationErrors.push(VALIDATION_MESSAGES.RATING_REQUIRED)
            } else {
              if (
                questionValue < question.minValue ||
                questionValue > question.maxValue
              ) {
                validationErrors.push(VALIDATION_MESSAGES.RATING_REQUIRED)
              }
            }
            break

          case 'multiple-choice':
          case 'dropdown':
            if (question.required && (!questionValue || questionValue === '')) {
              validationErrors.push(VALIDATION_MESSAGES.OPTION_REQUIRED)
            }
            break
        }
      }

      return {
        isValid: validationErrors.length === 0,
        errors: validationErrors,
      }
    },
    [getAnswer],
  )

  const validateStep = useCallback(
    (stepId: string): ValidationResult => {
      if (!state.config) {
        return { isValid: true, errors: [] }
      }

      const step = state.config.steps.find((s) => s.id === stepId)
      if (!step?.questions) {
        return { isValid: true, errors: [] }
      }

      const allErrors: string[] = []
      const stepErrors: Record<string, string> = {}

      step.questions.forEach((question) => {
        const validation = validateQuestion(question)
        if (!validation.isValid) {
          stepErrors[question.id] = validation.errors[0] // Take first error
          allErrors.push(...validation.errors)
        }
      })

      // Update errors in state
      if (Object.keys(stepErrors).length > 0) {
        dispatch({
          type: 'SET_ERRORS',
          payload: {
            ...state.errors,
            [stepId]: stepErrors,
          },
        })
      } else {
        // Clear errors for this step
        dispatch({ type: 'CLEAR_ERRORS', payload: stepId })
      }

      return {
        isValid: allErrors.length === 0,
        errors: allErrors,
      }
    },
    [state.config, state.errors, validateQuestion, dispatch],
  )

  const validateCurrentStep = useCallback((): ValidationResult => {
    const currentStep = getCurrentStep()
    if (!currentStep) {
      return { isValid: true, errors: [] }
    }
    return validateStep(currentStep.id)
  }, [getCurrentStep, validateStep])

  const validateAllSteps = useCallback((): ValidationResult => {
    if (!state.config) {
      return { isValid: true, errors: [] }
    }

    const allErrors: string[] = []
    let isValid = true

    state.config.steps.forEach((step) => {
      const stepValidation = validateStep(step.id)
      if (!stepValidation.isValid) {
        isValid = false
        allErrors.push(...stepValidation.errors)
      }
    })

    return {
      isValid,
      errors: allErrors,
    }
  }, [state.config, validateStep])

  const getQuestionError = useCallback(
    (questionId: string): string | undefined => {
      const currentStep = getCurrentStep()
      if (!currentStep) return undefined

      return errors[currentStep.id]?.[questionId]
    },
    [errors, getCurrentStep],
  )

  const getStepErrors = useCallback(
    (stepId: string): Record<string, string> => {
      return errors[stepId] || {}
    },
    [errors],
  )

  const hasErrors = useMemo(() => {
    return Object.keys(errors).some(
      (stepId) => Object.keys(errors[stepId]).length > 0,
    )
  }, [errors])

  const hasStepErrors = useCallback(
    (stepId: string): boolean => {
      return Object.keys(getStepErrors(stepId)).length > 0
    },
    [getStepErrors],
  )

  const hasQuestionError = useCallback(
    (questionId: string): boolean => {
      return getQuestionError(questionId) !== undefined
    },
    [getQuestionError],
  )

  const clearErrors = useCallback(
    (stepId?: string) => {
      if (stepId) {
        dispatch({ type: 'CLEAR_ERRORS', payload: stepId })
      } else {
        dispatch({ type: 'CLEAR_ERRORS' })
      }
    },
    [dispatch],
  )

  const clearQuestionError = useCallback(
    (questionId: string) => {
      const currentStep = getCurrentStep()
      if (!currentStep) return

      const stepErrors = getStepErrors(currentStep.id)
      if (stepErrors[questionId]) {
        const newStepErrors = { ...stepErrors }
        delete newStepErrors[questionId]

        dispatch({
          type: 'SET_ERRORS',
          payload: {
            ...errors,
            [currentStep.id]: newStepErrors,
          },
        })
      }
    },
    [getCurrentStep, getStepErrors, errors, dispatch],
  )

  return {
    errors,
    validateQuestion,
    validateStep,
    validateCurrentStep,
    validateAllSteps,
    getQuestionError,
    getStepErrors,
    hasErrors,
    hasStepErrors,
    hasQuestionError,
    clearErrors,
    clearQuestionError,
  }
}
