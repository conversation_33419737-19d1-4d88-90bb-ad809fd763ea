'use client'

import { Checkbox, Input, Label, RadioGroup, RadioGroupItem, Typography } from '@ttplatform/ui/components'
import { AnimatePresence, motion } from 'framer-motion'
import React, { useState } from 'react'
import { MultipleChoiceQuestion as MultipleChoiceQuestionType } from '../../types/survey.types'

interface MultipleChoiceQuestionProps {
  question: MultipleChoiceQuestionType
  value: string | string[]
  onChange: (value: string | string[]) => void
  error?: string
}

export default function MultipleChoiceQuestion({
  question,
  value = question.allowMultiple ? [] : '',
  onChange,
  // error,
}: MultipleChoiceQuestionProps) {
  const [otherValue, setOtherValue] = useState('')
  const [showOtherInput, setShowOtherInput] = useState(false)

  const isMultiple = question.allowMultiple
  const selectedValues = Array.isArray(value) ? value : [value].filter(Boolean)
  const hasOtherOption = question.hasOtherOption

  const handleOptionChange = (optionValue: string, checked: boolean) => {
    if (optionValue === 'other') {
      setShowOtherInput(checked)
      if (!checked) {
        setOtherValue('')
        // Remove other value from selection
        if (isMultiple) {
          const newValues = selectedValues.filter(
            (v) => !v.startsWith('other:'),
          )
          onChange(newValues)
        } else {
          onChange('')
        }
      }
      return
    }

    if (isMultiple) {
      const currentValues = selectedValues.filter(
        (v) => !v.startsWith('other:'),
      )
      if (checked) {
        onChange([...currentValues, optionValue])
      } else {
        onChange(currentValues.filter((v) => v !== optionValue))
      }
    } else {
      onChange(checked ? optionValue : '')
    }
  }

  const handleOtherInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newOtherValue = e.target.value
    setOtherValue(newOtherValue)

    if (newOtherValue.trim()) {
      const otherOptionValue = `other:${newOtherValue}`
      if (isMultiple) {
        const currentValues = selectedValues.filter(
          (v) => !v.startsWith('other:'),
        )
        onChange([...currentValues, otherOptionValue])
      } else {
        onChange(otherOptionValue)
      }
    } else {
      // Remove other value if input is empty
      if (isMultiple) {
        const newValues = selectedValues.filter((v) => !v.startsWith('other:'))
        onChange(newValues)
      } else {
        onChange('')
      }
    }
  }

  const isOptionSelected = (optionValue: string) => {
    if (optionValue === 'other') {
      return selectedValues.some((v) => v.startsWith('other:'))
    }
    return selectedValues.includes(optionValue)
  }

  React.useEffect(() => {
    // Initialize other input if there's an existing other value
    const otherSelected = selectedValues.find((v) => v.startsWith('other:'))
    if (otherSelected) {
      setShowOtherInput(true)
      setOtherValue(otherSelected.replace('other:', ''))
    }
  }, [selectedValues])

  return (
    <motion.div
      className="space-y-3"
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.3 }}
    >
      {isMultiple ? (
        // Multiple choice with checkboxes
        question.options.map((option, index) => (
          <motion.div
            key={option.id}
            className="flex items-center space-x-3"
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ delay: index * 0.1 }}
          >
            <Checkbox
              id={`${question.id}-${option.id}`}
              checked={isOptionSelected(option.value)}
              onCheckedChange={(checked) => handleOptionChange(option.value, !!checked)}
              className="data-[state=checked]:bg-primary data-[state=checked]:border-primary"
            />
            <Label
              htmlFor={`${question.id}-${option.id}`}
              className="cursor-pointer"
            >
              <Typography variant="body2" className="text-foreground">
                {option.label}
              </Typography>
            </Label>
          </motion.div>
        ))
      ) : (
        // Single choice with radio buttons
        <RadioGroup
          value={selectedValues[0] || ''}
          onValueChange={(value) => handleOptionChange(value, true)}
          className="space-y-3"
        >
          {question.options.map((option, index) => (
            <motion.div
              key={option.id}
              className="flex items-center space-x-3"
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: index * 0.1 }}
            >
              <RadioGroupItem
                value={option.value}
                id={`${question.id}-${option.id}`}
                className="text-primary"
              />
              <Label
                htmlFor={`${question.id}-${option.id}`}
                className="cursor-pointer"
              >
                <Typography variant="body2" className="text-foreground">
                  {option.label}
                </Typography>
              </Label>
            </motion.div>
          ))}
        </RadioGroup>
      )}

      {/* Other Option */}
      {hasOtherOption && (
        <motion.div
          initial={{ opacity: 0, x: -20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ delay: question.options.length * 0.1 }}
          className="space-y-3"
        >
          {isMultiple ? (
            <div className="flex items-center space-x-3">
              <Checkbox
                id={`${question.id}-other`}
                checked={showOtherInput}
                onCheckedChange={(checked) => handleOptionChange('other', !!checked)}
                className="data-[state=checked]:bg-primary data-[state=checked]:border-primary"
              />
              <Label htmlFor={`${question.id}-other`} className="cursor-pointer">
                <Typography variant="body2" className="text-foreground">
                  Khác
                </Typography>
              </Label>
            </div>
          ) : (
            <div className="flex items-center space-x-3">
              <RadioGroupItem
                value="other"
                id={`${question.id}-other`}
                className="text-primary"
                onClick={() => handleOptionChange('other', true)}
              />
              <Label htmlFor={`${question.id}-other`} className="cursor-pointer">
                <Typography variant="body2" className="text-foreground">
                  Khác
                </Typography>
              </Label>
            </div>
          )}

          <AnimatePresence>
            {showOtherInput && (
              <motion.div
                className="ml-7"
                initial={{ opacity: 0, height: 0 }}
                animate={{ opacity: 1, height: 'auto' }}
                exit={{ opacity: 0, height: 0 }}
                transition={{ duration: 0.3 }}
              >
                <Input
                  type="text"
                  value={otherValue}
                  onChange={handleOtherInputChange}
                  placeholder="Vui lòng ghi rõ..."
                  className="focus-visible:ring-primary"
                />
              </motion.div>
            )}
          </AnimatePresence>
        </motion.div>
      )}

      {/* Selection Summary for Multiple Choice */}
      {isMultiple && selectedValues.length > 0 && (
        <motion.div
          className="mt-4 p-3 bg-primary/5 rounded-lg border border-primary/20"
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.3 }}
        >
          <Typography variant="caption" className="text-primary font-medium mb-2 block">
            Đã chọn ({selectedValues.length}):
          </Typography>
          <div className="flex flex-wrap gap-2">
            {selectedValues.map((val, index) => (
              <span
                key={index}
                className="inline-flex items-center px-2 py-1 rounded-full text-xs bg-primary/10 text-primary border border-primary/20"
              >
                {val.startsWith('other:')
                  ? `Khác: ${val.replace('other:', '')}`
                  : question.options.find((opt) => opt.value === val)?.label ||
                  val}
              </span>
            ))}
          </div>
        </motion.div>
      )}
    </motion.div>
  )
}
