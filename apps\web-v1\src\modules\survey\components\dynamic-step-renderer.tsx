'use client'

import { STEP_TYPES } from '../constants/survey.constants'
import { StepConfig } from '../types/survey.types'

import BaseStep from './base-step'
// Import step components
import SurveyConfirmationStep from './survey-confirmation-step'
import SurveyPolicyStep from './survey-policy-step'
import SurveyQuestionsStep from './survey-questions-step'

interface DynamicStepRendererProps {
  step: StepConfig
  className?: string
}

export default function DynamicStepRenderer({
  step,
  className = '',
}: DynamicStepRendererProps) {
  const renderStepContent = () => {
    switch (step.type) {
      case STEP_TYPES.POLICY:
        return <SurveyPolicyStep stepConfig={step} />

      case STEP_TYPES.CONFIRMATION:
        return <SurveyConfirmationStep stepConfig={step} />

      case STEP_TYPES.QUESTIONS:
        return <SurveyQuestionsStep stepConfig={step} />

      case STEP_TYPES.CUSTOM:
        // For custom steps, render with BaseStep and custom content
        return (
          <BaseStep step={step} className={className}>
            {step.content && (
              <div
                className="prose prose-sm max-w-none"
                dangerouslySetInnerHTML={{ __html: step.content }}
              />
            )}
          </BaseStep>
        )

      default:
        console.warn(`Unknown step type: ${step.type}`)
        return (
          <BaseStep step={step} className={className}>
            <div className="p-4 border border-orange-300 rounded bg-orange-50">
              <p className="text-orange-600 text-sm">
                Unsupported step type: {step.type}
              </p>
              <p className="text-xs text-orange-500 mt-2">Step ID: {step.id}</p>
            </div>
          </BaseStep>
        )
    }
  }

  return (
    <div key={step.id} className={className}>
      {renderStepContent()}
    </div>
  )
}

// Multi-step renderer with animation
interface MultiStepRendererProps {
  steps: StepConfig[]
  currentStepIndex: number
  className?: string
}

export function MultiStepRenderer({
  steps,
  currentStepIndex,
  className = '',
}: MultiStepRendererProps) {
  const currentStep = steps[currentStepIndex]

  if (!currentStep) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <h2 className="text-xl font-bold text-gray-800 mb-2">
            Không tìm thấy bước này
          </h2>
          <p className="text-gray-600">
            Bước {currentStepIndex + 1} không tồn tại trong khảo sát này.
          </p>
        </div>
      </div>
    )
  }

  return (
    <DynamicStepRenderer
      key={currentStep.id}
      step={currentStep}
      className={className}
    />
  )
}
