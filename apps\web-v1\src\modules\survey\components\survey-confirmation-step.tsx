'use client'

import { MainButton } from '@ttplatform/core-page-builder/components'
import { Input, Label, Textarea, Typography } from '@ttplatform/ui/components'
import { useSurveyContext } from '../context/survey-context'
import { useSurveyValidation } from '../hooks'
import { useSurveyData } from '../hooks/use-survey-data'
import { useSurveyNavigation } from '../hooks/use-survey-navigation'
import BaseStep from './base-step'

interface SurveyConfirmationStepProps {
  stepConfig?: any
}

export default function SurveyConfirmationStep({
  stepConfig,
}: SurveyConfirmationStepProps) {
  const { getCurrentStep } = useSurveyContext()
  const { getAnswer, setAnswer } = useSurveyData()
  const { getQuestionError } = useSurveyValidation()
  const { handleButtonAction, canGoNext, canGoPrevious } = useSurveyNavigation()

  const step = stepConfig || getCurrentStep()

  if (!step) {
    return null
  }

  // Default form fields if not provided in config
  const defaultFields = [
    {
      id: 'contact_name',
      label: 'Họ tên người phụ trách',
      type: 'text',
      required: true,
    },
    {
      id: 'contact_phone',
      label: 'Số điện thoại người phụ trách',
      type: 'tel',
      required: true,
    },
    {
      id: 'contact_email',
      label: 'Email người phụ trách',
      type: 'email',
      required: true,
    },
    { id: 'company_name', label: 'Tên công ty', type: 'text', required: true },
    {
      id: 'company_address',
      label: 'Địa chỉ công ty',
      type: 'text',
      required: true,
    },
    {
      id: 'company_tax_code',
      label: 'Mã số thuế',
      type: 'text',
      required: false,
    },
  ]

  const fields = step.questions || defaultFields

  const handleInputChange = (fieldId: string, value: string) => {
    setAnswer(fieldId, value)
  }

  const renderField = (field: any) => {
    const value = getAnswer(field.id) || ''
    const error = getQuestionError(field.id)

    return (
      <div key={field.id}>
        <Label htmlFor={field.id} className="mb-2 text-gray-600">
          <Typography variant="caption">
            {field.title}
            {field.required && <span className="text-destructive ml-1">*</span>}
          </Typography>
        </Label>

        {field.type === 'textarea' ? (
          <Textarea
            id={field.id}
            value={value}
            onChange={(e) => handleInputChange(field.id, e.target.value)}
            className={`focus-visible:ring-primary resize-vertical border border-gray-200 ${error ? 'border-destructive focus-visible:ring-destructive' : ''}`}
            placeholder={field.placeholder}
            rows={field.rows || 3}
          />
        ) : (
          <Input
            id={field.id}
            type={field.type || 'text'}
            value={value}
            onChange={(e) => handleInputChange(field.id, e.target.value)}
            className={`bg-white border-gray-200 text-gray-800 ${error ? 'border-destructive focus-visible:ring-destructive h-12' : ''}`}
            placeholder={field.placeholder}
          />
        )}

        {error && (
          <Typography
            variant="caption"
            className="text-destructive !font-normal block mt-1"
          >
            {error}
          </Typography>
        )}
      </div>
    )
  }

  const renderStepButtons = (
    <>
      {canGoPrevious && (
        <MainButton
          label="QUAY LẠI"
          variant="secondary"
          isDisabledIcon
          className="!px-[22px]"
          onClick={() => handleButtonAction('previous')}
        />
      )}

      <MainButton
        variant="primary"
        label={canGoNext ? 'TIẾP TỤC' : 'GỬI'}
        isDisabledIcon
        className="!px-[22px]"
        onClick={() => handleButtonAction(canGoNext ? 'next' : 'submit')}
      />
    </>
  )

  return (
    <BaseStep step={step}>
      {/* Info text */}
      <Typography variant="body1">
        Quý khách hàng vui lòng kiểm tra lại các thông tin liên hệ và có thể
        chỉnh sửa lại nếu chưa chính xác.
        <br />
        Phú Thái Cat sẽ dựa vào những thông tin này để liên hệ với quý khách
        hàng và gửi quà tặng sau khi hoàn thành khảo sát.
      </Typography>

      {/* Line */}
      <div className="h-px w-full bg-gray-200 mx-auto"></div>

      {/* Form */}
      <form className="space-y-10">
        {/* Company & Person Info */}
        <section className="space-y-6">
          <Typography variant="h4">
            THÔNG TIN CÔNG TY VÀ NGƯỜI PHỤ TRÁCH
          </Typography>
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-6">
            {fields.map(renderField)}
          </div>
        </section>
      </form>

      {/* Line */}
      <div className="h-px w-full bg-gray-200 mx-auto"></div>
      <section className="space-y-6">
        <Typography variant="h4">THÔNG TIN GIAO DỊCH</Typography>
        <div className="grid grid-cols-1 sm:grid-cols-2 gap-6">
          <div>
            <Label htmlFor="transactionType" className="mb-2 text-gray-600">
              <Typography variant="caption">Loại giao dịch</Typography>
            </Label>
            <Input
              id="transactionType"
              type="text"
              defaultValue="Sửa chữa tại công trường"
              readOnly
              className="bg-[#eaecf0] h-12 text-gray-700 cursor-not-allowed"
            />
          </div>
          <div>
            <Label htmlFor="invoice" className="mb-2 text-gray-600">
              <Typography variant="caption">Số hóa đơn</Typography>
            </Label>
            <Input
              id="invoice"
              type="text"
              defaultValue="3W09362"
              readOnly
              className="bg-[#eaecf0] h-12 text-gray-700 cursor-not-allowed"
            />
          </div>
        </div>
        <div className="grid grid-cols-1 sm:grid-cols-2 gap-6">
          <div>
            <Label htmlFor="model" className="mb-2 text-gray-600">
              <Typography variant="caption">Model</Typography>
            </Label>
            <Input
              id="model"
              type="text"
              defaultValue="329D"
              readOnly
              className="bg-[#eaecf0] h-12 text-gray-700 cursor-not-allowed"
            />
          </div>
          <div>
            <Label htmlFor="serial" className="mb-2 text-gray-600">
              <Typography variant="caption">Serial</Typography>
            </Label>
            <Input
              id="serial"
              type="text"
              defaultValue="MNB00195"
              readOnly
              className="bg-[#eaecf0] h-12 text-gray-700 cursor-not-allowed"
            />
          </div>
        </div>
        <div>
          <Label htmlFor="description" className="mb-2 text-gray-600">
            <Typography variant="caption">Mô tả giao dịch</Typography>
          </Label>
          <Textarea
            id="description"
            rows={3}
            defaultValue="KIỂM TRA VÀ ĐƯA RA KHUYẾN NGHỊ BẢO DƯỠNG, SỬA CHỮA"
            readOnly
            className="bg-[#eaecf0] p-3 text-gray-700 cursor-not-allowed resize-none min-h-[100px]"
          />
        </div>
      </section>

      {/* Render Button */}
      {renderStepButtons && (
        <div className="flex justify-center space-x-4">{renderStepButtons}</div>
      )}
    </BaseStep>
  )
}
