'use client'

import { MainButton } from '@ttplatform/core-page-builder/components'
import { Input, Label, Textarea, Typography } from '@ttplatform/ui/components'
import { useSurveyContext } from '../context/survey-context'
import { useSurveyValidation } from '../hooks'
import { useSurveyData } from '../hooks/use-survey-data'
import { useSurveyNavigation } from '../hooks/use-survey-navigation'
import BaseStep from './base-step'

interface SurveyConfirmationStepProps {
  stepConfig?: any
}

export default function SurveyConfirmationStep({
  stepConfig,
}: SurveyConfirmationStepProps) {
  const { getCurrentStep } = useSurveyContext()
  const { getAnswer, setAnswer } = useSurveyData()
  const { getQuestionError } = useSurveyValidation()
  const { handleButtonAction, canGoNext, canGoPrevious } = useSurveyNavigation()

  const step = stepConfig || getCurrentStep()

  if (!step) {
    return null
  }

  // Default form fields if not provided in config
  const defaultFields = [
    {
      id: 'contact_name',
      label: 'Họ tên người phụ trách',
      type: 'text',
      required: true,
    },
    {
      id: 'contact_phone',
      label: 'Số điện thoại người phụ trách',
      type: 'tel',
      required: true,
    },
    {
      id: 'contact_email',
      label: 'Email người phụ trách',
      type: 'email',
      required: true,
    },
    { id: 'company_name', label: 'Tên công ty', type: 'text', required: true },
    {
      id: 'company_address',
      label: 'Địa chỉ công ty',
      type: 'text',
      required: true,
    },
    {
      id: 'company_tax_code',
      label: 'Mã số thuế',
      type: 'text',
      required: false,
    },
  ]

  const fields = step.questions || defaultFields

  const handleInputChange = (fieldId: string, value: string) => {
    setAnswer(fieldId, value)
  }

  const renderField = (field: any) => {
    const value = getAnswer(field.id) || ''
    const error = getQuestionError(field.id)

    return (
      <div key={field.id}>
        <Label htmlFor={field.id} className="text-foreground">
          <Typography variant="caption" className="text-foreground">
            {field.title}
            {field.required && <span className="text-destructive ml-1">*</span>}
          </Typography>
        </Label>

        {field.type === 'textarea' ? (
          <Textarea
            id={field.id}
            value={value}
            onChange={(e) => handleInputChange(field.id, e.target.value)}
            className={`focus-visible:ring-primary resize-vertical ${error ? 'border-destructive focus-visible:ring-destructive' : ''}`}
            placeholder={field.placeholder}
            rows={field.rows || 3}
          />
        ) : (
          <Input
            id={field.id}
            type={field.type || 'text'}
            value={value}
            onChange={(e) => handleInputChange(field.id, e.target.value)}
            className={`focus-visible:ring-primary ${error ? 'border-destructive focus-visible:ring-destructive' : ''}`}
            placeholder={field.placeholder}
          />
        )}

        {error && (
          <Typography variant="caption" className="text-destructive mt-1 block">
            {error}
          </Typography>
        )}
      </div>
    )
  }

  const renderStepButtons = (
    <>
      {canGoPrevious && (
        <MainButton
          label="QUAY LẠI"
          variant="secondary"
          isDisabledIcon
          className="!px-[22px]"
          onClick={() => handleButtonAction('previous')}
        />
      )}

      <MainButton
        variant="primary"
        label={canGoNext ? 'TIẾP TỤC' : 'GỬI'}
        isDisabledIcon
        className="!px-[22px]"
        onClick={() => handleButtonAction(canGoNext ? 'next' : 'submit')}
      />
    </>
  )

  return (
    <BaseStep step={step}>
      {/* Info text */}
      <div className="mb-8 space-y-3">
        <Typography variant="body1" className="text-foreground">
          Quý khách hàng vui lòng kiểm tra lại các thông tin liên hệ và có thể
          chỉnh sửa lại nếu chưa chính xác.
        </Typography>
        <Typography variant="body1" className="text-muted-foreground">
          Phú Thái Cat sẽ dựa vào những thông tin này để liên hệ với quý khách
          hàng và gửi quà tặng sau khi hoàn thành khảo sát.
        </Typography>
      </div>

      {/* Form */}
      <form className="space-y-10">
        {/* Company & Person Info */}
        <section>
          <Typography variant="h5" className="text-foreground mb-6">
            THÔNG TIN CÔNG TY VÀ NGƯỜI PHỤ TRÁCH
          </Typography>
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-6">
            {fields.map(renderField)}
          </div>
        </section>
      </form>

      <section>
        <Typography variant="h5" className="text-foreground mb-6">
          THÔNG TIN GIAO DỊCH
        </Typography>
        <div className="grid grid-cols-1 sm:grid-cols-2 gap-6 mb-4">
          <div>
            <Label htmlFor="transactionType">
              <Typography variant="caption" className="text-foreground">
                Loại giao dịch
              </Typography>
            </Label>
            <Input
              id="transactionType"
              type="text"
              defaultValue="Sửa chữa tại công trường"
              readOnly
              className="bg-muted text-muted-foreground cursor-not-allowed"
            />
          </div>
          <div>
            <Label htmlFor="invoice">
              <Typography variant="caption" className="text-foreground">
                Số hóa đơn
              </Typography>
            </Label>
            <Input
              id="invoice"
              type="text"
              defaultValue="3W09362"
              readOnly
              className="bg-muted text-muted-foreground cursor-not-allowed"
            />
          </div>
        </div>
        <div className="grid grid-cols-1 sm:grid-cols-2 gap-6 mb-4">
          <div>
            <Label htmlFor="model">
              <Typography variant="caption" className="text-foreground">
                Model
              </Typography>
            </Label>
            <Input
              id="model"
              type="text"
              defaultValue="329D"
              readOnly
              className="bg-muted text-muted-foreground cursor-not-allowed"
            />
          </div>
          <div>
            <Label htmlFor="serial">
              <Typography variant="caption" className="text-foreground">
                Serial
              </Typography>
            </Label>
            <Input
              id="serial"
              type="text"
              defaultValue="MNB00195"
              readOnly
              className="bg-muted text-muted-foreground cursor-not-allowed"
            />
          </div>
        </div>
        <div>
          <Label htmlFor="description">
            <Typography variant="caption" className="text-foreground">
              Mô tả giao dịch
            </Typography>
          </Label>
          <Textarea
            id="description"
            rows={3}
            defaultValue="KIỂM TRA VÀ ĐƯA RA KHUYẾN NGHỊ BẢO DƯỠNG, SỬA CHỮA"
            readOnly
            className="bg-muted text-muted-foreground cursor-not-allowed resize-none"
          />
        </div>
      </section>

      {/* Render Button */}
      {renderStepButtons && (
        <div className="flex justify-center space-x-4">{renderStepButtons}</div>
      )}
    </BaseStep>
  )
}
