'use client'

import { MainButton } from '@ttplatform/core-page-builder/components'
import { useSurveyContext } from '../context/survey-context'
import { useSurveyValidation } from '../hooks'
import { useSurveyData } from '../hooks/use-survey-data'
import { useSurveyNavigation } from '../hooks/use-survey-navigation'
import BaseStep from './base-step'

interface SurveyConfirmationStepProps {
  stepConfig?: any
}

export default function SurveyConfirmationStep({
  stepConfig,
}: SurveyConfirmationStepProps) {
  const { getCurrentStep } = useSurveyContext()
  const { getAnswer, setAnswer } = useSurveyData()
  const { getQuestionError } = useSurveyValidation()
  const { handleButtonAction, canGoNext, canGoPrevious } = useSurveyNavigation()

  const step = stepConfig || getCurrentStep()

  if (!step) {
    return null
  }

  // Default form fields if not provided in config
  const defaultFields = [
    {
      id: 'contact_name',
      label: 'Họ tên người phụ trách',
      type: 'text',
      required: true,
    },
    {
      id: 'contact_phone',
      label: '<PERSON><PERSON> điện thoại người phụ trách',
      type: 'tel',
      required: true,
    },
    {
      id: 'contact_email',
      label: 'Email người phụ trách',
      type: 'email',
      required: true,
    },
    { id: 'company_name', label: 'Tên công ty', type: 'text', required: true },
    {
      id: 'company_address',
      label: 'Địa chỉ công ty',
      type: 'text',
      required: true,
    },
    {
      id: 'company_tax_code',
      label: 'Mã số thuế',
      type: 'text',
      required: false,
    },
  ]

  const fields = step.questions || defaultFields

  const handleInputChange = (fieldId: string, value: string) => {
    setAnswer(fieldId, value)
  }

  const renderField = (field: any) => {
    const value = getAnswer(field.id) || ''
    const error = getQuestionError(field.id)

    return (
      <div key={field.id}>
        <label
          htmlFor={field.id}
          className="block text-xs text-gray-800 mb-2 font-normal"
        >
          {field.title}
          {field.required && <span className="text-red-500 ml-1">*</span>}
        </label>

        {field.type === 'textarea' ? (
          <textarea
            id={field.id}
            value={value}
            onChange={(e) => handleInputChange(field.id, e.target.value)}
            className={`w-full rounded-md border text-gray-800 bg-white text-sm px-4 py-2 focus:outline-none focus:ring-2 focus:ring-yellow-400 resize-vertical ${error ? 'border-red-500' : ''}`}
            placeholder={field.placeholder}
            rows={field.rows || 3}
          />
        ) : (
          <input
            id={field.id}
            type={field.type || 'text'}
            value={value}
            onChange={(e) => handleInputChange(field.id, e.target.value)}
            className={`w-full rounded-md border text-gray-800 bg-white text-sm px-4 py-2 focus:outline-none focus:ring-2 focus:ring-yellow-400 ${error ? 'border-red-500' : ''}`}
            placeholder={field.placeholder}
          />
        )}

        {error && <p className="text-red-500 text-xs mt-1">{error}</p>}
      </div>
    )
  }

  const renderStepButtons = (
    <>
      {canGoPrevious && (
        <MainButton
          label="QUAY LẠI"
          variant="secondary"
          isDisabledIcon
          className="!px-[22px]"
          onClick={() => handleButtonAction('previous')}
        />
      )}

      <MainButton
        variant="primary"
        label={canGoNext ? 'TIẾP TỤC' : 'GỬI'}
        isDisabledIcon
        className="!px-[22px]"
        onClick={() => handleButtonAction(canGoNext ? 'next' : 'submit')}
      />
    </>
  )

  return (
    <BaseStep step={step}>
      {/* Info text */}
      <div className="text-sm sm:text-base mb-8 leading-relaxed">
        <p className="mb-2">
          Quý khách hàng vui lòng kiểm tra lại các thông tin liên hệ và có thể
          chỉnh sửa lại nếu chưa chính xác.
        </p>
        <p>
          Phú Thái Cat sẽ dựa vào những thông tin này để liên hệ với quý khách
          hàng và gửi quà tặng sau khi hoàn thành khảo sát.
        </p>
      </div>

      {/* Form */}
      <form className="space-y-10">
        {/* Company & Person Info */}
        <section>
          <h2 className="text-[#1E293B] font-semibold text-lg mb-6">
            THÔNG TIN CÔNG TY VÀ NGƯỜI PHỤ TRÁCH
          </h2>
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-6">
            {fields.map(renderField)}
          </div>
        </section>
      </form>

      <section>
        <h2 className="text-[#1E293B] font-semibold text-lg mb-6">
          THÔNG TIN GIAO DỊCH
        </h2>
        <div className="grid grid-cols-1 sm:grid-cols-2 gap-6 mb-4">
          <div>
            <label
              htmlFor="transactionType"
              className="block text-xs text-gray-800 mb-2 font-normal"
            >
              Loại giao dịch
            </label>
            <input
              id="transactionType"
              type="text"
              defaultValue="Sửa chữa tại công trường"
              readOnly
              className="w-full rounded-md border text-gray-800 bg-[#eaecf0] text-sm px-4 py-2 cursor-not-allowed"
            />
          </div>
          <div>
            <label
              htmlFor="invoice"
              className="block text-xs text-gray-800 mb-2 font-normal"
            >
              Số hóa đơn
            </label>
            <input
              id="invoice"
              type="text"
              defaultValue="3W09362"
              readOnly
              className="w-full rounded-md border text-gray-800 bg-[#eaecf0] text-sm px-4 py-2 cursor-not-allowed"
            />
          </div>
        </div>
        <div className="grid grid-cols-1 sm:grid-cols-2 gap-6 mb-4">
          <div>
            <label
              htmlFor="model"
              className="block text-xs text-gray-800 mb-2 font-normal"
            >
              Model
            </label>
            <input
              id="model"
              type="text"
              defaultValue="329D"
              readOnly
              className="w-full rounded-md border text-gray-800 bg-[#eaecf0] text-sm px-4 py-2 cursor-not-allowed"
            />
          </div>
          <div>
            <label
              htmlFor="serial"
              className="block text-xs text-gray-800 mb-2 font-normal"
            >
              Serial
            </label>
            <input
              id="serial"
              type="text"
              defaultValue="MNB00195"
              readOnly
              className="w-full rounded-md border text-gray-800 bg-[#eaecf0] text-sm px-4 py-2 cursor-not-allowed"
            />
          </div>
        </div>
        <div>
          <label
            htmlFor="description"
            className="block text-xs text-gray-800 mb-2 font-normal"
          >
            Mô tả giao dịch
          </label>
          <textarea
            id="description"
            rows={3}
            defaultValue="KIỂM TRA VÀ ĐƯA RA KHUYẾN NGHỊ BẢO DƯỠNG, SỬA CHỮA"
            readOnly
            className="w-full rounded-md border text-gray-800 bg-[#eaecf0] text-xs sm:text-sm px-4 py-3 cursor-not-allowed resize-none"
          ></textarea>
        </div>
      </section>

      {/* Render Button */}
      {renderStepButtons && (
        <div className="flex justify-center space-x-4">{renderStepButtons}</div>
      )}
    </BaseStep>
  )
}
