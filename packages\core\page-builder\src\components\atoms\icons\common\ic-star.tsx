import React from 'react'

interface StarIconProps extends React.SVGProps<SVGSVGElement> {
  size?: number
  color?: string
}

export const StarIcon: React.FC<StarIconProps> = ({ size = 78, className }) => {
  return (
    <svg
      width={size}
      height={size}
      viewBox="0 0 78 78"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={className}
    >
      <g filter="url(#filter0_dddd_22446_29800)">
        <path
          d="M52.257 19.0165L43.6493 17.7017L39.7912 9.48369C39.503 8.87002 38.4962 8.87002 38.208 9.48369L34.351 17.7017L25.7433 19.0165C25.0363 19.125 24.754 19.9849 25.2498 20.4924L31.5032 26.902L30.025 35.9635C29.9072 36.6834 30.676 37.2235 31.3118 36.87L39.0002 32.621L46.6885 36.8712C47.3185 37.2212 48.0943 36.6915 47.9753 35.9647L46.4972 26.9032L52.7505 20.4935C53.2463 19.9849 52.9628 19.125 52.257 19.0165Z"
          fill="#FFB300"
        />
        <path
          d="M42.7441 18.127L42.9707 18.6094L43.498 18.6904L51.8652 19.9678L45.7812 26.2051L45.4287 26.5664L45.5098 27.0645L46.9463 35.8711L39.4844 31.7461L39 31.4785L38.5166 31.7461L31.0527 35.8701L32.4902 27.0635L32.5713 26.5645L32.2188 26.2041L26.1348 19.9678L34.502 18.6904L35.0293 18.6094L35.2559 18.127L38.999 10.1504L42.7441 18.127Z"
          stroke="#FFB300"
          strokeWidth="2"
        />
      </g>
      <defs>
        <filter
          id="filter0_dddd_22446_29800"
          x="0.700001"
          y="0.723438"
          width="76.6"
          height="76.5609"
          filterUnits="userSpaceOnUse"
          colorInterpolationFilters="sRGB"
        >
          <feFlood floodOpacity="0" result="BackgroundImageFix" />
          <feColorMatrix
            in="SourceAlpha"
            type="matrix"
            values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
            result="hardAlpha"
          />
          <feMorphology
            radius="4.5"
            operator="erode"
            in="SourceAlpha"
            result="effect1_dropShadow_22446_29800"
          />
          <feOffset dy="16" />
          <feGaussianBlur stdDeviation="14.4" />
          <feComposite in2="hardAlpha" operator="out" />
          <feColorMatrix
            type="matrix"
            values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.03 0"
          />
          <feBlend
            mode="normal"
            in2="BackgroundImageFix"
            result="effect1_dropShadow_22446_29800"
          />
          <feColorMatrix
            in="SourceAlpha"
            type="matrix"
            values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
            result="hardAlpha"
          />
          <feMorphology
            radius="4"
            operator="erode"
            in="SourceAlpha"
            result="effect2_dropShadow_22446_29800"
          />
          <feOffset dy="5.10612" />
          <feGaussianBlur stdDeviation="4.59551" />
          <feComposite in2="hardAlpha" operator="out" />
          <feColorMatrix
            type="matrix"
            values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.19 0"
          />
          <feBlend
            mode="normal"
            in2="effect1_dropShadow_22446_29800"
            result="effect2_dropShadow_22446_29800"
          />
          <feColorMatrix
            in="SourceAlpha"
            type="matrix"
            values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
            result="hardAlpha"
          />
          <feMorphology
            radius="2.25"
            operator="erode"
            in="SourceAlpha"
            result="effect3_dropShadow_22446_29800"
          />
          <feOffset dy="1.9316" />
          <feGaussianBlur stdDeviation="1.73845" />
          <feComposite in2="hardAlpha" operator="out" />
          <feColorMatrix
            type="matrix"
            values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.24 0"
          />
          <feBlend
            mode="normal"
            in2="effect2_dropShadow_22446_29800"
            result="effect3_dropShadow_22446_29800"
          />
          <feColorMatrix
            in="SourceAlpha"
            type="matrix"
            values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
            result="hardAlpha"
          />
          <feMorphology
            radius="1.125"
            operator="erode"
            in="SourceAlpha"
            result="effect4_dropShadow_22446_29800"
          />
          <feOffset dy="0.636953" />
          <feGaussianBlur stdDeviation="0.57326" />
          <feComposite in2="hardAlpha" operator="out" />
          <feColorMatrix
            type="matrix"
            values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.26 0"
          />
          <feBlend
            mode="normal"
            in2="effect3_dropShadow_22446_29800"
            result="effect4_dropShadow_22446_29800"
          />
          <feBlend
            mode="normal"
            in="SourceGraphic"
            in2="effect4_dropShadow_22446_29800"
            result="shape"
          />
        </filter>
      </defs>
    </svg>
  )
}
