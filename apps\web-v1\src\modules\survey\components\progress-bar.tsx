'use client'

import { useSurveyContext } from '../context/survey-context'
import { useSurveyNavigation } from '../hooks/use-survey-navigation'

interface ProgressBarProps {
  showStepNumbers?: boolean
  showStepTitles?: boolean
  allowClickNavigation?: boolean
  className?: string
}

export default function ProgressBar({ className = '' }: ProgressBarProps) {
  const { state } = useSurveyContext()
  const {
    currentStepIndex,
    //  totalSteps, goToStep
  } = useSurveyNavigation()

  if (!state.config || !state.config.settings.showProgressBar) {
    return null
  }

  const steps = state.config.steps

  const getStepStatus = (stepIndex: number) => {
    if (stepIndex < currentStepIndex) return 'completed'
    if (stepIndex === currentStepIndex) return 'current'
    return 'pending'
  }

  return (
    <div className={`my-6 ${className}`}>
      {/* Progress Bars */}
      <div className="flex items-center gap-2">
        {steps.map((step, index) => {
          const status = getStepStatus(index)
          let bgColor = '#D1D5DB' // gray-300 - pending
          if (status === 'completed' || status === 'current') {
            bgColor = '#FBBF24' // yellow-400
          }

          return (
            <div
              key={step.id}
              className="flex-1 h-1 rounded-full"
              style={{ backgroundColor: bgColor }}
            />
          )
        })}
      </div>

      {/* Subtitle - positioned under the active step */}
      <div className="mt-4">
        <div className="flex">
          {steps.map((step, index) => {
            const status = getStepStatus(index)
            return (
              <div key={step.id} className="flex-1 text-center">
                {status === 'current' && step.subtitle && (
                  <p className="text-xs text-gray-700 font-semibold">
                    {step.subtitle}
                  </p>
                )}
              </div>
            )
          })}
        </div>
      </div>
    </div>
  )
}

// Preset configurations for different use cases
export const ProgressBarPresets = {
  // Simple bar (original design)
  simple: {
    showStepNumbers: false,
    showStepTitles: false,
    allowClickNavigation: false,
  },

  // With step numbers
  numbered: {
    showStepNumbers: true,
    showStepTitles: false,
    allowClickNavigation: true,
  },

  // With step titles
  titled: {
    showStepNumbers: false,
    showStepTitles: true,
    allowClickNavigation: true,
  },

  // Full featured
  full: {
    showStepNumbers: true,
    showStepTitles: true,
    allowClickNavigation: true,
  },
}

// Convenience components for common use cases
export const SimpleProgressBar = (
  props: Omit<ProgressBarProps, keyof typeof ProgressBarPresets.simple>,
) => <ProgressBar {...ProgressBarPresets.simple} {...props} />

export const NumberedProgressBar = (
  props: Omit<ProgressBarProps, keyof typeof ProgressBarPresets.numbered>,
) => <ProgressBar {...ProgressBarPresets.numbered} {...props} />

export const TitledProgressBar = (
  props: Omit<ProgressBarProps, keyof typeof ProgressBarPresets.titled>,
) => <ProgressBar {...ProgressBarPresets.titled} {...props} />

export const FullProgressBar = (
  props: Omit<ProgressBarProps, keyof typeof ProgressBarPresets.full>,
) => <ProgressBar {...ProgressBarPresets.full} {...props} />
