'use client'

import { motion } from 'framer-motion'
import React from 'react'
import { OpenEndedQuestion as OpenEndedQuestionType } from '../../types/survey.types'

interface OpenEndedQuestionProps {
  question: OpenEndedQuestionType
  value: string
  onChange: (value: string) => void
  error?: string
}

export default function OpenEndedQuestion({
  question,
  value = '',
  onChange,
  error,
}: OpenEndedQuestionProps) {
  const handleChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>,
  ) => {
    const newValue = e.target.value

    // Apply max length validation
    if (question.maxLength && newValue.length > question.maxLength) {
      return
    }

    onChange(newValue)
  }

  const isTextarea = question.maxLength && question.maxLength > 100
  const remainingChars = question.maxLength
    ? question.maxLength - value.length
    : null

  return (
    <motion.div
      className="space-y-2"
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.3 }}
    >
      {isTextarea ? (
        <textarea
          value={value}
          onChange={handleChange}
          placeholder={question.placeholder}
          className={`w-full rounded-md border text-gray-800 bg-white text-sm px-4 py-2 focus:outline-none focus:ring-2 focus:ring-yellow-400 resize-vertical ${error ? 'border-red-500' : ''}`}
          rows={4}
          minLength={question.minLength}
          maxLength={question.maxLength}
        />
      ) : (
        <input
          type="text"
          value={value}
          onChange={handleChange}
          placeholder={question.placeholder}
          className={`w-full rounded-md border text-gray-800 bg-white text-sm px-4 py-2 focus:outline-none focus:ring-2 focus:ring-yellow-400 ${error ? 'border-red-500' : ''}`}
          minLength={question.minLength}
          maxLength={question.maxLength}
        />
      )}

      {/* Character count */}
      {question.maxLength && (
        <div className="flex justify-between text-xs text-gray-500">
          <span>
            {question.minLength && <>Tối thiểu {question.minLength} ký tự</>}
          </span>
          <span
            className={
              remainingChars && remainingChars < 20 ? 'text-orange-500' : ''
            }
          >
            {value.length}/{question.maxLength}
          </span>
        </div>
      )}

      {/* Length validation hint */}
      {question.minLength &&
        value.length > 0 &&
        value.length < question.minLength && (
          <p className="text-orange-500 text-xs">
            Vui lòng nhập ít nhất {question.minLength} ký tự
          </p>
        )}
    </motion.div>
  )
}
