'use client'

import { Input, Textarea, Typography } from '@ttplatform/ui/components'
import { motion } from 'framer-motion'
import React from 'react'
import { OpenEndedQuestion as OpenEndedQuestionType } from '../../types/survey.types'

interface OpenEndedQuestionProps {
  question: OpenEndedQuestionType
  value: string
  onChange: (value: string) => void
  error?: string
}

export default function OpenEndedQuestion({
  question,
  value = '',
  onChange,
  error,
}: OpenEndedQuestionProps) {
  const handleChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>,
  ) => {
    const newValue = e.target.value

    // Apply max length validation
    if (question.maxLength && newValue.length > question.maxLength) {
      return
    }

    onChange(newValue)
  }

  const isTextarea = question.maxLength && question.maxLength > 100
  const remainingChars = question.maxLength
    ? question.maxLength - value.length
    : null

  return (
    <motion.div
      className="space-y-3"
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.3 }}
    >
      {isTextarea ? (
        <Textarea
          value={value}
          onChange={handleChange}
          placeholder={question.placeholder}
          className={`focus-visible:ring-primary resize-vertical ${error ? 'border-destructive focus-visible:ring-destructive' : ''}`}
          rows={4}
          minLength={question.minLength}
          maxLength={question.maxLength}
        />
      ) : (
        <Input
          type="text"
          value={value}
          onChange={handleChange}
          placeholder={question.placeholder}
          className={`focus-visible:ring-primary ${error ? 'border-destructive focus-visible:ring-destructive' : ''}`}
          minLength={question.minLength}
          maxLength={question.maxLength}
        />
      )}

      {/* Character count */}
      {question.maxLength && (
        <div className="flex justify-between">
          <Typography variant="caption" className="text-muted-foreground">
            {question.minLength && <>Tối thiểu {question.minLength} ký tự</>}
          </Typography>
          <Typography
            variant="caption"
            className={
              remainingChars && remainingChars < 20
                ? 'text-destructive'
                : 'text-muted-foreground'
            }
          >
            {value.length}/{question.maxLength}
          </Typography>
        </div>
      )}

      {/* Length validation hint */}
      {question.minLength &&
        value.length > 0 &&
        value.length < question.minLength && (
          <Typography variant="caption" className="text-destructive">
            Vui lòng nhập ít nhất {question.minLength} ký tự
          </Typography>
        )}
    </motion.div>
  )
}
