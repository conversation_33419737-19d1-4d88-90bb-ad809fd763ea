'use client'

import {
  Input,
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectTrigger,
  SelectValue,
  Typography,
} from '@ttplatform/ui/components'
import { AnimatePresence, motion } from 'framer-motion'
import React, { useEffect, useRef, useState } from 'react'
import { DropdownQuestion as DropdownQuestionType } from '../../types/survey.types'

interface DropdownQuestionProps {
  question: DropdownQuestionType
  value: string
  onChange: (value: string) => void
  error?: string
}

export default function DropdownQuestion({
  question,
  value = '',
  onChange,
  error,
}: DropdownQuestionProps) {
  const [isOpen, setIsOpen] = useState(false)
  const [otherValue, setOtherValue] = useState('')
  const [showOtherInput, setShowOtherInput] = useState(false)
  const dropdownRef = useRef<HTMLDivElement>(null)

  const hasOtherOption = question.hasOtherOption
  const isOtherSelected = value.startsWith('other:')

  useEffect(() => {
    // Initialize other input if there's an existing other value
    if (isOtherSelected) {
      setShowOtherInput(true)
      setOtherValue(value.replace('other:', ''))
    }
  }, [value, isOtherSelected])

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        dropdownRef.current &&
        !dropdownRef.current.contains(event.target as Node)
      ) {
        setIsOpen(false)
      }
    }

    document.addEventListener('mousedown', handleClickOutside)
    return () => document.removeEventListener('mousedown', handleClickOutside)
  }, [])

  const handleOptionSelect = (optionValue: string) => {
    if (optionValue === 'other') {
      setShowOtherInput(true)
      setIsOpen(false)
      if (!otherValue.trim()) {
        onChange('')
      } else {
        onChange(`other:${otherValue}`)
      }
    } else {
      setShowOtherInput(false)
      setOtherValue('')
      onChange(optionValue)
      setIsOpen(false)
    }
  }

  const handleOtherInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newOtherValue = e.target.value
    setOtherValue(newOtherValue)

    if (newOtherValue.trim()) {
      onChange(`other:${newOtherValue}`)
    } else {
      onChange('')
    }
  }

  const getDisplayValue = () => {
    if (!value) return 'Chọn một tùy chọn...'

    if (isOtherSelected) {
      return `Khác: ${value.replace('other:', '')}`
    }

    const option = question.options.find((opt) => opt.value === value)
    return option?.label || value
  }

  const getSelectedOption = () => {
    if (isOtherSelected) return null
    return question.options.find((opt) => opt.value === value)
  }

  return (
    <motion.div
      className="space-y-3"
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.3 }}
    >
      {/* Dropdown */}
      <Select
        value={isOtherSelected ? 'other' : value}
        onValueChange={handleOptionSelect}
      >
        <SelectTrigger
          className={`w-full !h-12 focus:ring-primary ${error ? 'border-destructive focus:ring-destructive' : ''}`}
        >
          <SelectValue placeholder="Select">
            <Typography variant="body2" className="text-foreground truncate">
              {getDisplayValue()}
            </Typography>
          </SelectValue>
        </SelectTrigger>
        <SelectContent className="max-h-60 w-full">
          <SelectGroup>
            {question.options.map((option) => (
              <SelectItem
                key={option.id}
                value={option.value}
                className="focus:bg-primary/10 focus:text-primary"
              >
                <Typography variant="body2">
                  {option.label}
                </Typography>
              </SelectItem>
            ))}

            {hasOtherOption && (
              <SelectItem
                value="other"
                className="focus:bg-primary/10 focus:text-primary border-t"
              >
                <Typography variant="body2">
                  Khác...
                </Typography>
              </SelectItem>
            )}
          </SelectGroup>
        </SelectContent>
      </Select>

      {/* Other Input */}
      <AnimatePresence>
        {showOtherInput && (
          <motion.div
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: 'auto' }}
            exit={{ opacity: 0, height: 0 }}
            transition={{ duration: 0.3 }}
          >
            <Input
              type="text"
              value={otherValue}
              onChange={handleOtherInputChange}
              placeholder="Vui lòng ghi rõ..."
              className="focus-visible:ring-primary"
            />
          </motion.div>
        )}
      </AnimatePresence>

      {/* Selected Value Display */}
      {value && (
        <motion.div
          className="p-3 bg-primary/5 rounded-lg border border-primary/20"
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
        >
          <Typography variant="caption" className="text-primary mb-1 block">
            Đã chọn:
          </Typography>
          <Typography variant="body2" className="font-medium text-foreground">
            {getDisplayValue()}
          </Typography>
        </motion.div>
      )}
    </motion.div>
  )
}
