'use client'

import { AnimatePresence, motion } from 'framer-motion'
import React, { useEffect, useRef, useState } from 'react'
import { DropdownQuestion as DropdownQuestionType } from '../../types/survey.types'

interface DropdownQuestionProps {
  question: DropdownQuestionType
  value: string
  onChange: (value: string) => void
  error?: string
}

export default function DropdownQuestion({
  question,
  value = '',
  onChange,
  error,
}: DropdownQuestionProps) {
  const [isOpen, setIsOpen] = useState(false)
  const [otherValue, setOtherValue] = useState('')
  const [showOtherInput, setShowOtherInput] = useState(false)
  const dropdownRef = useRef<HTMLDivElement>(null)

  const hasOtherOption = question.hasOtherOption
  const isOtherSelected = value.startsWith('other:')

  useEffect(() => {
    // Initialize other input if there's an existing other value
    if (isOtherSelected) {
      setShowOtherInput(true)
      setOtherValue(value.replace('other:', ''))
    }
  }, [value, isOtherSelected])

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        dropdownRef.current &&
        !dropdownRef.current.contains(event.target as Node)
      ) {
        setIsOpen(false)
      }
    }

    document.addEventListener('mousedown', handleClickOutside)
    return () => document.removeEventListener('mousedown', handleClickOutside)
  }, [])

  const handleOptionSelect = (optionValue: string) => {
    if (optionValue === 'other') {
      setShowOtherInput(true)
      setIsOpen(false)
      if (!otherValue.trim()) {
        onChange('')
      } else {
        onChange(`other:${otherValue}`)
      }
    } else {
      setShowOtherInput(false)
      setOtherValue('')
      onChange(optionValue)
      setIsOpen(false)
    }
  }

  const handleOtherInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newOtherValue = e.target.value
    setOtherValue(newOtherValue)

    if (newOtherValue.trim()) {
      onChange(`other:${newOtherValue}`)
    } else {
      onChange('')
    }
  }

  const getDisplayValue = () => {
    if (!value) return 'Chọn một tùy chọn...'

    if (isOtherSelected) {
      return `Khác: ${value.replace('other:', '')}`
    }

    const option = question.options.find((opt) => opt.value === value)
    return option?.label || value
  }

  const getSelectedOption = () => {
    if (isOtherSelected) return null
    return question.options.find((opt) => opt.value === value)
  }

  return (
    <motion.div
      className="space-y-3"
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.3 }}
    >
      {/* Dropdown */}
      <div className="relative" ref={dropdownRef}>
        <button
          type="button"
          onClick={() => setIsOpen(!isOpen)}
          className={`
            w-full rounded-md border text-gray-800 bg-white text-sm px-4 py-2 focus:outline-none focus:ring-2 focus:ring-yellow-400
            ${error ? 'border-red-500' : ''}
            flex items-center justify-between cursor-pointer
            ${!value ? 'text-gray-500' : 'text-gray-800'}
          `}
        >
          <span className="truncate">{getDisplayValue()}</span>
          <motion.svg
            className="w-5 h-5 text-gray-400"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
            animate={{ rotate: isOpen ? 180 : 0 }}
            transition={{ duration: 0.2 }}
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M19 9l-7 7-7-7"
            />
          </motion.svg>
        </button>

        <AnimatePresence>
          {isOpen && (
            <motion.div
              className="absolute z-10 w-full mt-1 bg-white border border-gray-300 rounded-md shadow-lg max-h-60 overflow-auto"
              initial={{ opacity: 0, y: -10 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -10 }}
              transition={{ duration: 0.2 }}
            >
              {question.options.map((option, index) => (
                <motion.button
                  key={option.id}
                  type="button"
                  onClick={() => handleOptionSelect(option.value)}
                  className={`
                    w-full px-4 py-2 text-left text-sm hover:bg-gray-50 transition-colors
                    ${getSelectedOption()?.value === option.value ? 'bg-yellow-50 text-yellow-800' : 'text-gray-800'}
                  `}
                  initial={{ opacity: 0, x: -10 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ delay: index * 0.05 }}
                >
                  {option.label}
                </motion.button>
              ))}

              {hasOtherOption && (
                <motion.button
                  type="button"
                  onClick={() => handleOptionSelect('other')}
                  className={`
                    w-full px-4 py-2 text-left text-sm hover:bg-gray-50 transition-colors border-t border-gray-200
                    ${isOtherSelected ? 'bg-yellow-50 text-yellow-800' : 'text-gray-800'}
                  `}
                  initial={{ opacity: 0, x: -10 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ delay: question.options.length * 0.05 }}
                >
                  Khác...
                </motion.button>
              )}
            </motion.div>
          )}
        </AnimatePresence>
      </div>

      {/* Other Input */}
      <AnimatePresence>
        {showOtherInput && (
          <motion.div
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: 'auto' }}
            exit={{ opacity: 0, height: 0 }}
            transition={{ duration: 0.3 }}
          >
            <input
              type="text"
              value={otherValue}
              onChange={handleOtherInputChange}
              placeholder="Vui lòng ghi rõ..."
              className="w-full rounded-md border text-gray-800 bg-white text-sm px-4 py-2 focus:outline-none focus:ring-2 focus:ring-yellow-400"
            />
          </motion.div>
        )}
      </AnimatePresence>

      {/* Selected Value Display */}
      {value && (
        <motion.div
          className="p-3 bg-gray-50 rounded-lg border border-gray-200"
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
        >
          <p className="text-xs text-gray-600 mb-1">Đã chọn:</p>
          <p className="text-sm font-medium text-gray-800">
            {getDisplayValue()}
          </p>
        </motion.div>
      )}
    </motion.div>
  )
}
