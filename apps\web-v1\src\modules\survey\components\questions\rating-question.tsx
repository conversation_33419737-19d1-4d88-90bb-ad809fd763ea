'use client'

import { StarIcon } from '@ttplatform/core-page-builder/components'
import { Button, Typography } from '@ttplatform/ui/components'
import { motion } from 'framer-motion'
import React, { useCallback, useState } from 'react'
import {
  RATING_CONFIG,
  RATING_LABELS,
  RATING_SCALE,
} from '../../constants/survey.constants'
import { useSurveyData } from '../../hooks/use-survey-data'
import {
  RatingLevel,
  RatingQuestion as RatingQuestionType,
} from '../../types/survey.types'

interface RatingQuestionProps {
  question: RatingQuestionType
  value: number
  onChange: (value: number) => void
  error?: string
}

export default function RatingQuestion({
  question,
  value = 0,
  onChange,
  // error,
}: RatingQuestionProps) {
  const [isDragging, setIsDragging] = useState(false)
  const [hoverValue, setHoverValue] = useState<number | null>(null)
  // const [dragStartX, setDragStartX] = useState(0)
  const [sliderRect, setSliderRect] = useState<DOMRect | null>(null)
  const { shouldShowFollowUp } = useSurveyData()

  const minValue = question.minValue || RATING_SCALE.min
  const maxValue = question.maxValue || RATING_SCALE.max
  const showFollowUp = shouldShowFollowUp(question.id)

  const getRatingLevel = (rating: number): RatingLevel => {
    if (rating >= RATING_CONFIG.thresholds.satisfied) return 'satisfied'
    if (rating >= RATING_CONFIG.thresholds.neutral) return 'neutral'
    return 'dissatisfied'
  }

  const getRatingColor = (rating: number): string => {
    const level = getRatingLevel(rating)
    return RATING_CONFIG.colors[level]
  }

  const getRatingLabel = (rating: number): string => {
    const level = getRatingLevel(rating)
    return RATING_LABELS[level]
  }

  const currentRating = hoverValue || value
  const currentColor =
    currentRating > 0 ? getRatingColor(currentRating) : '#D1D5DB'
  const currentLabel =
    currentRating > 0 ? getRatingLabel(currentRating) : 'Chưa đánh giá'

  const handleSliderChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newValue = Number(e.target.value)
    onChange(newValue)
  }

  const handleMouseEnter = (rating: number) => {
    if (!isDragging) {
      setHoverValue(rating)
    }
  }

  const handleMouseLeave = () => {
    if (!isDragging) {
      setHoverValue(null)
    }
  }

  const handleClick = (rating: number) => {
    onChange(rating)
  }

  const handleStarMouseDown = (e: React.MouseEvent) => {
    e.preventDefault()
    setIsDragging(true)
    const sliderElement = e.currentTarget.closest(
      '.rating-slider',
    ) as HTMLElement
    if (sliderElement) {
      setSliderRect(sliderElement.getBoundingClientRect())
    }
  }

  const handleMouseMove = useCallback(
    (e: MouseEvent) => {
      if (!isDragging || !sliderRect) return

      requestAnimationFrame(() => {
        const x = e.clientX - sliderRect.left
        const percentage = Math.max(0, Math.min(1, x / sliderRect.width))
        const newValue = Math.round(
          minValue + percentage * (maxValue - minValue),
        )

        if (
          newValue >= minValue &&
          newValue <= maxValue &&
          newValue !== value
        ) {
          onChange(newValue)
        }
      })
    },
    [isDragging, sliderRect, minValue, maxValue, value, onChange],
  )

  const handleMouseUp = useCallback(() => {
    setIsDragging(false)
    setSliderRect(null)
  }, [])

  // Add global mouse events for dragging
  // biome-ignore lint/correctness/useExhaustiveDependencies: <explanation>
  React.useEffect(() => {
    if (isDragging) {
      document.addEventListener('mousemove', handleMouseMove)
      document.addEventListener('mouseup', handleMouseUp)
      return () => {
        document.removeEventListener('mousemove', handleMouseMove)
        document.removeEventListener('mouseup', handleMouseUp)
      }
    }
  }, [isDragging])

  // Generate scale numbers
  const scaleNumbers = Array.from(
    { length: maxValue - minValue + 1 },
    (_, i) => minValue + i,
  )

  // Calculate star position to align with numbers using fixed percentages
  const getStarPosition = (rating: number) => {
    // For 10-point scale (1-10), use fixed percentages that align with justify-between layout
    const positions = {
      1: 0,      // 0%
      2: 11.11,  // ~11%
      3: 22.22,  // ~22%
      4: 33.33,  // ~33%
      5: 44.44,  // ~44%
      6: 55.56,  // ~56%
      7: 66.67,  // ~67%
      8: 77.78,  // ~78%
      9: 88.89,  // ~89%
      10: 100    // 100%
    };

    return positions[rating as keyof typeof positions] || 50;
  };

  return (
    <motion.div
      className="space-y-6"
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.3 }}
    >
      {/* Rating Scale Info */}
      {question.showLabels !== false && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.1 }}
        >
          <Typography variant="body1" className="mb-3 text-foreground">
            Các câu hỏi về sự hài lòng của khách hàng được đánh giá theo thang
            điểm {maxValue} với ý nghĩa như sau:
          </Typography>
          <ul className="space-y-2">
            <li className="flex items-center space-x-2">
              <div
                className="w-3 h-3 rounded-full flex-shrink-0"
                style={{ backgroundColor: RATING_CONFIG.colors.satisfied }}
              ></div>
              <Typography variant="body2" className="text-foreground">
                <span className="font-semibold">Hài lòng:</span>{' '}
                {RATING_CONFIG.thresholds.satisfied} - {maxValue} điểm
              </Typography>
            </li>
            <li className="flex items-center space-x-2">
              <div
                className="w-3 h-3 rounded-full flex-shrink-0"
                style={{ backgroundColor: RATING_CONFIG.colors.neutral }}
              ></div>
              <Typography variant="body2" className="text-foreground">
                <span className="font-semibold">Bình thường:</span>{' '}
                {RATING_CONFIG.thresholds.neutral} -{' '}
                {RATING_CONFIG.thresholds.satisfied - 1} điểm
              </Typography>
            </li>
            <li className="flex items-center space-x-2">
              <div
                className="w-3 h-3 rounded-full flex-shrink-0"
                style={{ backgroundColor: RATING_CONFIG.colors.dissatisfied }}
              ></div>
              <Typography variant="body2" className="text-foreground">
                <span className="font-semibold">Không hài lòng:</span>{' '}
                {minValue} - {RATING_CONFIG.thresholds.neutral - 1} điểm
              </Typography>
            </li>
          </ul>
        </motion.div>
      )}

      {/* Rating Slider */}
      <motion.div
        className="relative rating-slider"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.2 }}
      >
        {/* Hidden Input Slider */}
        <input
          type="range"
          min={minValue}
          max={maxValue}
          value={value}
          onChange={handleSliderChange}
          onMouseDown={() => setIsDragging(true)}
          onMouseUp={() => setIsDragging(false)}
          className="slider absolute top-0 left-0 w-full h-1 opacity-0 cursor-pointer z-10"
        />

        {/* Gradient Background */}
        <div className="h-[6px] rounded-full bg-gradient-to-r from-[#D92D20] via-[#EEDE77] to-[#079455]"></div>

        {/* Scale Numbers */}
        <div className="mt-3 flex justify-between select-none">
          {scaleNumbers.map((num) => (
            <Button
              key={num}
              variant="ghost"
              size="sm"
              onClick={() => handleClick(num)}
              onMouseEnter={() => handleMouseEnter(num)}
              onMouseLeave={handleMouseLeave}
              className="hover:bg-primary/10 hover:text-primary rounded px-2 py-1 transition-colors cursor-pointer relative group font-medium h-auto"
            >
              <Typography
                variant="body2"
                className="text-muted-foreground group-hover:text-primary"
              >
                {num}
              </Typography>
              {/* Tooltip - chỉ hiện khi không có rating */}
              {hoverValue === num && value === 0 && (
                <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-2 py-1 bg-popover border border-border text-popover-foreground text-xs rounded whitespace-nowrap z-20 shadow-md">
                  <Typography variant="caption">
                    {getRatingLabel(num)}
                  </Typography>
                  <div className="absolute top-full left-1/2 transform -translate-x-1/2 w-0 h-0 border-l-2 border-r-2 border-t-2 border-transparent border-t-border"></div>
                </div>
              )}
            </Button>
          ))}
        </div>

        {/* Current Rating Indicator */}
        {currentRating > 0 && (
          <motion.div
            className="absolute flex flex-col items-center"
            style={{
              left: `${getStarPosition(currentRating)}%`,
              // Remove or adjust transform to avoid overcompensation
              transform: 'translateX(0)', // Remove -50% to align with the left edge of the position
              top: '-4rem',
            }}
            initial={{ opacity: 0, scale: 0 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.2 }}
          >
            {/* Rating Label */}
            <div
              className="px-3 py-1 rounded-lg text-white text-sm font-medium mb-2 relative h-10 flex items-center justify-center"
              style={{ backgroundColor: currentColor }}
            >
              {currentLabel}
              {/* Arrow pointing down */}
              <div
                className="absolute top-full left-1/2 transform -translate-x-1/2 w-0 h-0 border-l-4 border-r-4 border-t-4 border-transparent"
                style={{ borderTopColor: currentColor }}
              ></div>
            </div>

            {/* Star Icon */}
            <motion.div
              className="text-xl cursor-grab active:cursor-grabbing select-none"
              style={{ color: currentColor }}
              animate={{ scale: isDragging ? 1.3 : 1 }}
              transition={{ duration: 0.1 }}
              onMouseDown={handleStarMouseDown}
            >
              <StarIcon />
            </motion.div>
          </motion.div>
        )}
      </motion.div>

      {/* Custom Styles for Slider */}
      <style jsx>{`
        .slider::-webkit-slider-thumb {
          appearance: none;
          width: 20px;
          height: 20px;
          border-radius: 50%;
          background: ${currentColor};
          border: 2px solid white;
          box-shadow: 0 2px 4px rgba(0,0,0,0.2);
          cursor: pointer;
          transition: all 0.1s ease;
        }
        
        .slider::-webkit-slider-thumb:hover {
          transform: scale(1.1);
          box-shadow: 0 4px 8px rgba(0,0,0,0.3);
        }
        
        .slider::-moz-range-thumb {
          width: 20px;
          height: 20px;
          border-radius: 50%;
          background: ${currentColor};
          border: 2px solid white;
          box-shadow: 0 2px 4px rgba(0,0,0,0.2);
          cursor: pointer;
          transition: all 0.1s ease;
        }
        
        .slider::-moz-range-thumb:hover {
          transform: scale(1.1);
          box-shadow: 0 4px 8px rgba(0,0,0,0.3);
        }
      `}</style>
    </motion.div>
  )
}
