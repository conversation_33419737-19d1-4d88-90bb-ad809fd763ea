'use client'

import { MainButton } from '@ttplatform/core-page-builder/components'
import { Typography } from '@ttplatform/ui/components'
import { useSurveyContext } from '../context/survey-context'
import { useSurveyNavigation } from '../hooks/use-survey-navigation'
import BaseStep from './base-step'
import { QuestionsList } from './question-renderer'

interface SurveyQuestionsStepProps {
  stepConfig?: any
}

export default function SurveyQuestionsStep({
  stepConfig,
}: SurveyQuestionsStepProps) {
  const { getCurrentStep } = useSurveyContext()
  const { handleButtonAction, canGoNext, canGoPrevious } = useSurveyNavigation()

  const step = stepConfig || getCurrentStep()

  if (!step || !step.questions) {
    return (
      <BaseStep
        step={
          step || {
            id: 'questions',
            title: 'Câu hỏi khảo sát',
            type: 'questions',
            order: 1,
          }
        }
      >
        <div className="text-center py-8">
          <Typography variant="body1" className="text-muted-foreground">
            <PERSON>h<PERSON>ng có câu hỏi nào được cấu hình cho bước này.
          </Typography>
        </div>
      </BaseStep>
    )
  }

  const renderStepButtons = (
    <>
      {canGoPrevious && (
        <MainButton
          variant="secondary"
          label="QUAY LẠI"
          isDisabledIcon
          className="!px-[22px]"
          onClick={() => handleButtonAction('previous')}
        />
      )}

      <MainButton
        variant="primary"
        label={canGoNext ? 'TIẾP TỤC' : 'GỬI'}
        isDisabledIcon
        className="!px-[22px]"
        onClick={() => handleButtonAction(canGoNext ? 'next' : 'submit')}
      />
    </>
  )

  return (
    <BaseStep step={step}>
      {/* Questions List */}
      <QuestionsList questions={step.questions} className="space-y-10" />

      {/* Render Button */}
      {renderStepButtons && (
        <div className="flex justify-center space-x-4">{renderStepButtons}</div>
      )}
    </BaseStep>
  )
}
